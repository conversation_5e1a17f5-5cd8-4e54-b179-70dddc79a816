#!/usr/bin/env python3
"""
Update batch 7 analysis results in database - partial (Ghidra timeout issues)
"""

from packet_analysis_db import PacketAnalysisDB

# Initialize database
db = PacketAnalysisDB()

print("Updating batch 7 functions (partial due to Ghidra timeout)...")

# Function 1: Guild Action (analyzed)
db.update_function_analysis(
    address="00680feb",
    opcode_hex="0x3B1",
    opcode_dec=945,
    function_name="send_guild_action_opcode_945",
    notes="Guild action with string validation and processing",
    ghidra_comment="Sends guild action with opcode 0x3B1 (945) and string validation."
)

# Function 2: Empty Packet (analyzed)
db.update_function_analysis(
    address="00681303",
    opcode_hex="0xE8",
    opcode_dec=232,
    function_name="send_empty_packet_opcode_232",
    notes="Empty packet with zero length",
    ghidra_comment="Sends empty packet with opcode 0xE8 (232) and zero length."
)

# Function 3: Trade/Guild Handler (analyzed)
db.update_function_analysis(
    address="0068150c",
    opcode_hex="0x3A/0x3B3",
    opcode_dec=58,
    function_name="handle_trade_guild_opcode_58_947",
    notes="Handles trade (0x3A) and guild (0x3B3) operations",
    ghidra_comment="Handles trade/guild with opcodes 0x3A (58) and 0x3B3 (947)."
)

# Function 4: Same as above (duplicate)
db.update_function_analysis(
    address="00681597",
    opcode_hex="0x3A/0x3B3",
    opcode_dec=58,
    function_name="handle_trade_guild_opcode_58_947_duplicate",
    notes="Duplicate of trade/guild handler",
    ghidra_comment="Duplicate function - same as handle_trade_guild_opcode_58_947."
)

# Function 5: Data Packet (analyzed)
db.update_function_analysis(
    address="0068263d",
    opcode_hex="0x191",
    opcode_dec=401,
    function_name="send_data_opcode_401",
    notes="Data packet with parameters",
    ghidra_comment="Sends data with opcode 0x191 (401) and parameters."
)

# Function 6: Character Status (analyzed)
db.update_function_analysis(
    address="006829b5",
    opcode_hex="0x315",
    opcode_dec=789,
    function_name="send_character_status_opcode_789",
    notes="Character status with loop processing",
    ghidra_comment="Sends character status with opcode 0x315 (789) and loop processing."
)

# Function 7: Character Action Multiple (analyzed)
db.update_function_analysis(
    address="00682cfa",
    opcode_hex="0x159",
    opcode_dec=345,
    function_name="send_character_action_opcode_345_multiple",
    notes="Character action with multiple packets and timing",
    ghidra_comment="Sends character action with opcode 0x159 (345) multiple packets."
)

# Function 8: Same as above (duplicate)
db.update_function_analysis(
    address="00682df9",
    opcode_hex="0x159",
    opcode_dec=345,
    function_name="send_character_action_opcode_345_multiple_duplicate",
    notes="Duplicate of character action multiple",
    ghidra_comment="Duplicate function - same as send_character_action_opcode_345_multiple."
)

# Function 9: Same as above (duplicate)
db.update_function_analysis(
    address="00682ef0",
    opcode_hex="0x159",
    opcode_dec=345,
    function_name="send_character_action_opcode_345_multiple_duplicate2",
    notes="Duplicate of character action multiple",
    ghidra_comment="Duplicate function - same as send_character_action_opcode_345_multiple."
)

# Function 10: NPC Interaction (analyzed)
db.update_function_analysis(
    address="00695369",
    opcode_hex="0x152",
    opcode_dec=338,
    function_name="send_npc_interaction_opcode_338",
    notes="NPC interaction with conditional logic",
    ghidra_comment="Sends NPC interaction with opcode 0x152 (338) and conditional logic."
)

# Functions 11-16: Ghidra timeout - mark as pending analysis
for i, addr in enumerate(["006957bc", "006987e5", "00698955", "0069c309", "0069c60f", "0069c88a"], 11):
    db.update_function_analysis(
        address=addr,
        opcode_hex="TIMEOUT",
        opcode_dec=0,
        function_name=f"FUN_{addr}_timeout",
        notes="Ghidra timeout - needs re-analysis",
        ghidra_comment="Analysis failed due to Ghidra timeout."
    )

# Show updated progress
progress = db.get_progress()
print(f"\n📊 Updated Analysis Progress:")
print(f"Total Functions: {progress['total_functions']}")
print(f"Analyzed: {progress['analyzed_functions']} ({progress['completion_percentage']}%)")
print(f"Pending: {progress['pending_functions']}")

# Export updated data
db.export_to_csv("func_analysis_status_updated.csv")
print("\n⚠️ Batch 7 partially completed - Ghidra timeout issues!")
