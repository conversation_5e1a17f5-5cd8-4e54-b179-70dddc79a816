def decompress_lz77_packet_data(compressed_data: bytes) -> bytes:
    """
    Giải nén dữ liệu packet sử dụng thuật toán LZ77

    Args:
        compressed_data: Dữ liệu đã nén dưới dạng bytes

    Returns:
        bytes: Dữ liệu đã giải nén

    Raises:
        ValueError: Nếu dữ liệu không hợp lệ hoặc bị lỗi trong quá trình giải nén
    """
    if not compressed_data:
        return b''

    # Khởi tạo các biến
    input_pos = 0
    output = bytearray()
    input_len = len(compressed_data)

    while input_pos < input_len:
        # Đọc byte điều khiển
        if input_pos >= input_len:
            break

        control_byte = compressed_data[input_pos]
        input_pos += 1

        if control_byte < 0x20:
            # Literal bytes: copy trực tiếp
            literal_count = control_byte + 1

            # Kiểm tra bounds
            if input_pos + literal_count > input_len:
                raise ValueError("Dữ liệu nén không hợp lệ: không đủ literal bytes")

            # Copy literal bytes
            for i in range(literal_count):
                output.append(compressed_data[input_pos])
                input_pos += 1

        else:
            # Back-reference: copy từ dữ liệu đã giải nén trước đó

            # Tính toán length
            length = (control_byte >> 5)
            if length == 7:
                # Extended length
                if input_pos >= input_len:
                    raise ValueError("Dữ liệu nén không hợp lệ: thiếu extended length byte")
                length = compressed_data[input_pos] + 7
                input_pos += 1

            # Đọc offset byte
            if input_pos >= input_len:
                raise ValueError("Dữ liệu nén không hợp lệ: thiếu offset byte")
            offset_byte = compressed_data[input_pos]
            input_pos += 1

            # Tính toán offset
            # Trong code C: local_10 = local_8 + (((uVar4 & 0x1f) * -0x100 + -1) - (uint)*local_c);
            # Điều này có nghĩa là: offset = -((control_byte & 0x1f) * 256 + offset_byte + 1)
            # Vì đây là offset âm (đi ngược lại), ta tính như sau:
            offset = ((control_byte & 0x1f) << 8) | offset_byte
            offset += 1

            # Kiểm tra offset hợp lệ
            if offset > len(output):
                raise ValueError(f"Offset không hợp lệ: {offset} > {len(output)}")

            # Vị trí bắt đầu copy
            copy_start = len(output) - offset
            if copy_start < 0:
                raise ValueError("Offset vượt quá dữ liệu có sẵn")

            # Copy 2 bytes đầu tiên (theo logic trong code C)
            if copy_start + 1 >= len(output):
                raise ValueError("Không đủ dữ liệu để copy 2 bytes đầu")

            output.append(output[copy_start])
            output.append(output[copy_start + 1])
            copy_pos = copy_start + 2

            # Copy các bytes còn lại
            for i in range(length):
                if copy_pos >= len(output):
                    # Nếu copy_pos vượt quá, copy từ dữ liệu vừa được thêm vào
                    copy_pos = copy_start + (copy_pos - copy_start) % offset
                output.append(output[copy_pos])
                copy_pos += 1

    return bytes(output)


def test_lz77_decompressor():
    """
    Hàm test cơ bản cho decompressor
    """
    print("Bắt đầu test LZ77 decompressor...")

    # Test với dữ liệu rỗng
    result = decompress_lz77_packet_data(b'')
    assert result == b'', f"Test dữ liệu rỗng thất bại: {result}"
    print("✓ Test dữ liệu rỗng: PASS")

    # Test với literal bytes đơn giản
    # Control byte 0x02 = copy 3 literal bytes
    test_data = b'\x02ABC'
    result = decompress_lz77_packet_data(test_data)
    expected = b'ABC'
    assert result == expected, f"Test literal thất bại: {result} != {expected}"
    print(f"✓ Test literal: {test_data.hex()} -> {result} : PASS")

    # Test với literal bytes khác
    test_data = b'\x04HELLO'
    result = decompress_lz77_packet_data(test_data)
    expected = b'HELLO'
    assert result == expected, f"Test literal 2 thất bại: {result} != {expected}"
    print(f"✓ Test literal 2: {test_data.hex()} -> {result} : PASS")

    print("Tất cả các test cơ bản đã PASS!")


def decompress_packet_from_hex(hex_string: str) -> bytes:
    """
    Tiện ích để giải nén từ hex string

    Args:
        hex_string: Chuỗi hex của dữ liệu nén

    Returns:
        bytes: Dữ liệu đã giải nén
    """
    # Loại bỏ khoảng trắng và chuyển thành bytes
    hex_clean = hex_string.replace(' ', '').replace('\n', '')
    compressed_data = bytes.fromhex(hex_clean)
    return decompress_lz77_packet_data(compressed_data)


if __name__ == "__main__":
    test_lz77_decompressor()

    print("\n" + "="*50)
    print("LZ77 Decompressor sẵn sàng sử dụng!")
    print("="*50)
    print("Cách sử dụng:")
    print("1. decompress_lz77_packet_data(compressed_bytes) -> bytes")
    print("2. decompress_packet_from_hex(hex_string) -> bytes")
    print("="*50)

    # Demo với dữ liệu thực tế (nếu có)
    print("\nDemo:")
    print("Để test với dữ liệu thực tế, hãy sử dụng:")
    print("result = decompress_lz77_packet_data(your_compressed_data)")
    print("hoặc")
    print("result = decompress_packet_from_hex('your_hex_string')")
    print("\nVí dụ:")
    print("data = bytes([0x02, 0x41, 0x42, 0x43])  # Literal 'ABC'")
    print("result = decompress_lz77_packet_data(data)")
    print("print(result)  # b'ABC'")