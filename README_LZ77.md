# LZ77 Packet Decompressor

<PERSON><PERSON><PERSON> là phiên b<PERSON><PERSON> củ<PERSON> hàm `decompress_lz77_packet_data` đ<PERSON><PERSON><PERSON> reverse engineer t<PERSON>.

## <PERSON><PERSON> tả

Hàm này thực hiện gi<PERSON>i nén dữ liệu packet sử dụng thuật toán LZ77. <PERSON><PERSON> nhận đầu vào là dữ liệu đã nén dưới dạng `bytes[]` và trả về dữ liệu đã giải nén cũng dưới dạng `bytes[]`.

## Cách sử dụng

### 1. Import module

```python
from lz77_decompressor import decompress_lz77_packet_data, decompress_packet_from_hex
```

### 2. G<PERSON><PERSON>i nén từ bytes

```python
# Dữ liệu nén dưới dạng bytes
compressed_data = bytes([0x02, 0x41, 0x42, 0x43])  # Literal "ABC"
decompressed = decompress_lz77_packet_data(compressed_data)
print(decompressed)  # b'ABC'
```

### 3. <PERSON><PERSON><PERSON><PERSON> nén từ hex string

```python
# Dữ liệu nén dưới dạng hex string
hex_data = "02414243"  # Literal "ABC"
decompressed = decompress_packet_from_hex(hex_data)
print(decompressed)  # b'ABC'
```

## Cấu trúc thuật toán LZ77

### Control Byte

Mỗi packet bắt đầu bằng một control byte:

- **< 0x20 (32)**: Literal bytes
  - Số lượng bytes = control_byte + 1
  - Copy trực tiếp các bytes tiếp theo

- **>= 0x20**: Back-reference (LZ77 compression)
  - Length = control_byte >> 5
  - Nếu length == 7: đọc thêm 1 byte cho extended length
  - Offset được tính từ 5 bit thấp của control byte và byte tiếp theo

### Ví dụ

```
Control byte: 0x02
- < 0x20 → Literal mode
- Length = 0x02 + 1 = 3 bytes
- Copy 3 bytes tiếp theo trực tiếp

Control byte: 0x20
- >= 0x20 → Back-reference mode
- Length = 0x20 >> 5 = 1
- Offset từ 5 bit thấp (0x00) và byte tiếp theo
```

## Files

- `lz77_decompressor.py`: Module chính chứa hàm giải nén
- `demo_lz77.py`: Script demo và test
- `README_LZ77.md`: Tài liệu hướng dẫn

## Chạy demo

```bash
python demo_lz77.py
```

## Test

```bash
python lz77_decompressor.py
```

## Lưu ý

- Hàm sẽ raise `ValueError` nếu dữ liệu không hợp lệ
- Thuật toán được reverse engineer từ code C trong Ghidra
- Đã test với các trường hợp cơ bản, có thể cần điều chỉnh cho dữ liệu phức tạp hơn

## Troubleshooting

Nếu gặp lỗi khi giải nén:

1. Kiểm tra dữ liệu đầu vào có đúng format không
2. Đảm bảo dữ liệu không bị truncate
3. Kiểm tra offset và length có hợp lệ không

## Liên hệ

Nếu có vấn đề hoặc cần hỗ trợ, hãy kiểm tra lại code C gốc trong Ghidra để đối chiếu.