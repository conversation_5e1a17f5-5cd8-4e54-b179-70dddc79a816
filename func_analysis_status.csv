"Opcode_Hex","Opcode_Dec","Function_Name","Location","Label","Code_Unit","Context","Notes","Analysis_Status"
"0x18B6","6326","send_zone_request_opcode_6326","005736f5","","CALL send_packet_with_header_encryption","UNCOND<PERSON>IONAL_CALL","Zone request packet","ANALYZED"
"0x533","1331","send_packet_check_request_opcode_1331","0058f8a7","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Packet check request","ANALYZED"
"0xD3","211","send_login_retry_opcode_211","00591dd9","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Login retry packet","ANALYZED"
"0xD3","211","send_login_retry_opcode_211","00592080","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Same function as above","ANALYZED"
"0x1606,0xD7,0x20","5638,215,32","send_game_initialization_multiple_opcodes","005928fd","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Game initialization with multiple opcodes","ANALYZED"
"0x1606,0xD7,0x20","5638,215,32","send_game_initialization_multiple_opcodes","00592958","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Same function as above","ANALYZED"
"0x1606,0xD7,0x20","5638,215,32","send_game_initialization_multiple_opcodes","005929c6","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Same function as above","ANALYZED"
"0x5117","20759","send_character_spawn_opcode_20759","00594f36","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Character spawn packet","ANALYZED"
"0x5116","20758","send_character_spawn_opcode_20758","0059675a","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Character spawn packet","ANALYZED"
"0xDA","218","send_login_auth_opcode_218","00591c90","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Login authentication","ANALYZED"
"0x02","2","send_disconnect_opcode_2","005a1b50","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Disconnect packet","ANALYZED"
"0x05","5","send_game_start_opcode_5","005a1c30","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Game start packet","ANALYZED"
"0x1D,0x07","29,7","send_movement_opcode_29_or_7","005a1d30","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Character movement","ANALYZED"
"0xE0","224","send_world_update_opcode_224","005a1e30","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","World update","ANALYZED"
"0x212","530","handle_character_info_opcode_530","005a1f30","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Character info handler","ANALYZED"
"0x180","384","send_request_opcode_384","005a2030","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","General request","ANALYZED"
"0x1639","5689","send_server_unity_opcode_5684","005a2130","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Server unity packet","ANALYZED"
"0x389","905","process_input_and_send_screenshot_opcode_905","005a2230","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Input processing with screenshot","ANALYZED"
"N/A","N/A","send_packet_with_header_encryption","007ac8b0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Main packet encryption function","ANALYZED"
"0x1D,0x07","29,7","send_movement_opcode_29_or_7","0083e670","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Character movement","ANALYZED"
"0x3C","60","send_item_drop_opcode_60","008634b0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Item drop","ANALYZED"
"0x8F","143","game_main_loop_send_device_lost_opcode_143","008cd500","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Main game loop with device lost","ANALYZED"
"0x3126","12582","send_inventory_update_opcode_12582","00b34a40","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Inventory update","ANALYZED"
"0x16","22","send_anticheat_check_opcode_22","00c099e0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Anti-cheat validation","ANALYZED"
"0x10","16","send_clear_inventory_opcode_16","00c6b080","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Clear inventory","ANALYZED"
"0x50,0x135","80,309","send_character_select_opcode_80_subcode_309","00648540","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Character selection","ANALYZED"
"0x09","9","send_attack_opcode_9","00670660","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Attack command","ANALYZED"
"0x08,0x119","8,281","send_skill_use_opcode_8_subcode_281","00673ef0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Skill/ability use","ANALYZED"
"0x50","80","send_character_action_opcode_80","00677d90","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Character actions","ANALYZED"
"0x29C","668","send_party_list_opcode_668","006b1380","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Party member list","ANALYZED"
"0x5110","20752","send_guild_action_opcode_20752","00718d30","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Guild actions","ANALYZED"
"0x90","144","send_ui_request_opcode_144","00725950","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","UI request","ANALYZED"
"0x2214","8724","send_npc_interaction_opcode_8724","0072c0d0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","NPC interaction","ANALYZED"
"0x500","1280","handle_raid_dungeon_ui_opcode_1280","00749720","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Raid/dungeon UI handler","ANALYZED"
"0x500","1280","send_raid_party_action_opcode_1280","007502e0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Raid party actions","ANALYZED"
"0x2120","8480","send_reset_data_opcode_8480","00757a90","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Reset game data","ANALYZED"
"0x47,0x09","71,9","send_movement_position_opcode_71_or_9","0083c7b0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Movement with position data","ANALYZED"
"0x1042","4162","send_status_change_opcode_4162","00875e00","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Status change notification","ANALYZED"
"0x529","1321","send_item_action_opcode_1321","00884140","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Item action/manipulation","ANALYZED"
"0x50","80","handle_skill_magic_ui_opcode_80","006781d0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Skill/magic UI handler with dual-skill system","ANALYZED"
"0x50","80","send_character_action_opcode_80_extended","0067b1b0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Extended character action handler","ANALYZED"
"0x50","80","send_character_special_action_opcode_80","006a31a0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Special character actions with validation","ANALYZED"
"0x150","336","send_game_action_opcode_336","006a62a0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Various game actions (5 types)","ANALYZED"
"0x1806","6150","send_object_interaction_opcode_6150","006e9680","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Object interaction and NPC interaction","ANALYZED"
"0x18BA","6330","send_zone_change_opcode_6330","006ff4c0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Zone change and teleportation","ANALYZED"
"0x50","80","send_pet_action_opcode_80","007051e0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Pet actions and commands","ANALYZED"
"0x08","8","send_chat_message_opcode_8","00c9d5d0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Chat message with text validation","ANALYZED"
"0xD4","212","handle_web_browser_ui_opcode_212","00ca5390","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Web browser UI and file operations","ANALYZED"
"0x90","144","send_ui_request_opcode_144_v2","00cbede0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Alternative UI request handler","ANALYZED"
"0x3A","58","handle_trade_exchange_opcode_58","00781fc0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Trade/exchange system with validation","ANALYZED"
"0x50","80","send_character_action_opcode_80_152","00784150","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Character action with code 0x98 (152)","ANALYZED"
"0x3040","12352","send_character_data_opcode_12352","00a7b480","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Comprehensive character data (0x768 bytes)","ANALYZED"
"0xFF","255","send_system_packet_opcode_255","00a95090","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","System-level communication","ANALYZED"
"0x1770","6000","send_position_validation_opcode_6000","00aa8dc0","","CALL send_packet_with_header_encryption","UNCONDITIONAL_CALL","Position validation and anti-cheat","ANALYZED"
