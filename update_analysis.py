#!/usr/bin/env python3
"""
Update analysis results in database
"""

from packet_analysis_db import PacketAnalysisDB

# Initialize database
db = PacketAnalysisDB()

# Update analyzed functions
print("Updating analyzed functions...")

# Function 1: Game Start
db.update_function_analysis(
    address="00598124",
    opcode_hex="0x05",
    opcode_dec=5,
    function_name="send_game_start_opcode_5",
    notes="Game initialization and start sequence",
    ghidra_comment="Handles game start with opcode 0x05 (5). Initializes game state, loads resources, and sends start packet."
)

# Function 2: User Info Update  
db.update_function_analysis(
    address="0059d62c",
    opcode_hex="0x302",
    opcode_dec=770,
    function_name="send_user_info_update_opcode_770",
    notes="Send user information update",
    ghidra_comment="Sends user info update with opcode 0x302 (770). Updates user data and status information."
)

# Function 3: World Update
db.update_function_analysis(
    address="0059f56c", 
    opcode_hex="0xE0",
    opcode_dec=224,
    function_name="send_world_update_opcode_224",
    notes="Complex world update with character spawning and management",
    ghidra_comment="Sends world update with opcode 0xE0 (224). Large function handling character spawning, world state updates, and player management."
)

# Mark failed functions (timeout)
db.mark_function_failed("005a1f38", "Ghidra timeout during decompilation")
db.mark_function_failed("005a239b", "Ghidra timeout during decompilation")

# Batch 2: Update 8 new analyzed functions
print("Updating batch 2 functions...")

# Function 4: System Status
db.update_function_analysis(
    address="005a9e91",
    opcode_hex="0x520",
    opcode_dec=1312,
    function_name="send_system_status_opcode_1312",
    notes="Basic system status packet",
    ghidra_comment="Sends system status with opcode 0x520 (1312). Basic system status packet with default values."
)

# Function 5: System Status v2
db.update_function_analysis(
    address="005a9faf",
    opcode_hex="0x520",
    opcode_dec=1312,
    function_name="send_system_status_opcode_1312_v2",
    notes="Conditional system status packet",
    ghidra_comment="Sends system status with opcode 0x520 (1312) variant 2. Conditional status based on parameter."
)

# Function 6: RSA Encrypted Packet
db.update_function_analysis(
    address="00628080",
    opcode_hex="0x55F",
    opcode_dec=1375,
    function_name="send_rsa_encrypted_packet_opcode_1375",
    notes="RSA encryption and secure packet transmission",
    ghidra_comment="Sends RSA encrypted packet with opcode 0x55F (1375). Handles RSA encryption and secure packet transmission."
)

# Function 7: Character Action 302
db.update_function_analysis(
    address="0064764f",
    opcode_hex="0x50",
    opcode_dec=80,
    function_name="send_character_action_opcode_80_302",
    notes="Character action with code 0x12E (302)",
    ghidra_comment="Sends character action with opcode 0x50 (80) and action 0x12E (302). Character validation and action processing."
)

# Function 8: UI Action 300
db.update_function_analysis(
    address="00647cfc",
    opcode_hex="0x90",
    opcode_dec=144,
    function_name="send_ui_action_opcode_144_300",
    notes="UI action with value 300",
    ghidra_comment="Sends UI action with opcode 0x90 (144) and value 300. UI interaction and parameter setting."
)

# Function 9: Character Select (already renamed)
db.update_function_analysis(
    address="006486d5",
    opcode_hex="0x50",
    opcode_dec=80,
    function_name="send_character_select_opcode_80_subcode_309",
    notes="Character selection with subcode 0x135 (309)",
    ghidra_comment="Handles character selection with opcode 0x50 (80) and subcode 0x135 (309). Character selection logic and validation."
)

# Function 10: Trade Action
db.update_function_analysis(
    address="00648acc",
    opcode_hex="0x3A",
    opcode_dec=58,
    function_name="handle_trade_action_opcode_58",
    notes="Trade validation and processing",
    ghidra_comment="Handles trade actions with opcode 0x3A (58). Trade validation and processing logic."
)

# Function 11: Character Action 300
db.update_function_analysis(
    address="00648d25",
    opcode_hex="0x50",
    opcode_dec=80,
    function_name="send_character_action_opcode_80_300",
    notes="Character action with code 300",
    ghidra_comment="Sends character action with opcode 0x50 (80) and action 300. Character action processing with conditional logic."
)

# Show updated progress
progress = db.get_progress()
print(f"\n📊 Updated Analysis Progress:")
print(f"Total Functions: {progress['total_functions']}")
print(f"Analyzed: {progress['analyzed_functions']} ({progress['completion_percentage']}%)")
print(f"Pending: {progress['pending_functions']}")

# Export updated data
db.export_to_csv("func_analysis_status_updated.csv")
print("\n✅ Database updated and exported to func_analysis_status_updated.csv")
