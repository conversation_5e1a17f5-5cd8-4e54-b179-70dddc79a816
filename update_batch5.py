#!/usr/bin/env python3
"""
Update batch 5 analysis results in database
"""

from packet_analysis_db import PacketAnalysisDB

# Initialize database
db = PacketAnalysisDB()

print("Updating batch 5 functions...")

# Mixed systems: File Management continuation + Pet/Mount + Zone Management

# Function 1: File Sync
db.update_function_analysis(
    address="00665e9b",
    opcode_hex="0x4E6",
    opcode_dec=1254,
    function_name="send_file_sync_opcode_1254",
    notes="File synchronization with zero parameters",
    ghidra_comment="Sends file sync with opcode 0x4E6 (1254) and action 7. File synchronization with zero parameters."
)

# Function 2: File Init
db.update_function_analysis(
    address="0066600b",
    opcode_hex="0x4E4",
    opcode_dec=1252,
    function_name="send_file_init_opcode_1252",
    notes="File initialization with zero parameters",
    ghidra_comment="Sends file init with opcode 0x4E4 (1252) and action 7. File initialization with zero parameters."
)

# Function 3: Pet/Mount System
db.update_function_analysis(
    address="006662dd",
    opcode_hex="0x4B9",
    opcode_dec=1209,
    function_name="send_pet_mount_opcode_1209",
    notes="Pet/mount system with validation and error handling",
    ghidra_comment="Sends pet/mount with opcode 0x4B9 (1209). Handles pet/mount system with validation and error handling."
)

# Function 4: Zone Action (Conditional)
db.update_function_analysis(
    address="006668ac",
    opcode_hex="0x5106/0x5120",
    opcode_dec=20742,
    function_name="send_zone_action_opcode_20742_20768",
    notes="Conditional zone operations (opcodes 20742/20768)",
    ghidra_comment="Sends zone action with opcodes 0x5106/0x5120 (20742/20768). Conditional zone operations based on parameter."
)

# Function 5: Zone Action (Duplicate - mark as duplicate)
db.update_function_analysis(
    address="00666941",
    opcode_hex="0x5106/0x5120",
    opcode_dec=20742,
    function_name="send_zone_action_opcode_20742_20768_duplicate",
    notes="Duplicate of zone action function",
    ghidra_comment="Duplicate function - same as send_zone_action_opcode_20742_20768."
)

# Function 6: Zone Status
db.update_function_analysis(
    address="00666a97",
    opcode_hex="0x5104",
    opcode_dec=20740,
    function_name="send_zone_status_opcode_20740",
    notes="Zone status update with byte parameter",
    ghidra_comment="Sends zone status with opcode 0x5104 (20740). Zone status update with byte parameter."
)

# Function 7: File Complete
db.update_function_analysis(
    address="00666b7e",
    opcode_hex="0x4DA",
    opcode_dec=1242,
    function_name="send_file_complete_opcode_1242",
    notes="File operation completion signal",
    ghidra_comment="Sends file complete with opcode 0x4DA (1242) and action 7. File operation completion signal."
)

# Function 8: File Cleanup
db.update_function_analysis(
    address="00666cee",
    opcode_hex="0x4D8",
    opcode_dec=1240,
    function_name="send_file_cleanup_opcode_1240",
    notes="File cleanup and finalization",
    ghidra_comment="Sends file cleanup with opcode 0x4D8 (1240) and action 7. File cleanup and finalization."
)

# Show updated progress
progress = db.get_progress()
print(f"\n📊 Updated Analysis Progress:")
print(f"Total Functions: {progress['total_functions']}")
print(f"Analyzed: {progress['analyzed_functions']} ({progress['completion_percentage']}%)")
print(f"Pending: {progress['pending_functions']}")

# Export updated data
db.export_to_csv("func_analysis_status_updated.csv")
print("\n✅ Batch 5 completed - Pet/Mount + Zone Management systems discovered!")
