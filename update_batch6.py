#!/usr/bin/env python3
"""
Update batch 6 analysis results in database - 16 functions
"""

from packet_analysis_db import PacketAnalysisDB

# Initialize database
db = PacketAnalysisDB()

print("Updating batch 6 functions (16 functions)...")

# Function 1: File Basic
db.update_function_analysis(
    address="00666ef7",
    opcode_hex="0x4C1",
    opcode_dec=1217,
    function_name="send_file_basic_opcode_1217",
    notes="File basic operation with action 7",
    ghidra_comment="Sends file basic with opcode 0x4C1 (1217) and action 7."
)

# Function 2: Complex UI Handler
db.update_function_analysis(
    address="0066932a",
    opcode_hex="Multiple",
    opcode_dec=0,
    function_name="handle_ui_events_complex",
    notes="Complex UI event handler with multiple opcodes",
    ghidra_comment="Complex UI event handler with multiple opcodes and file operations."
)

# Function 3: Attack (already renamed)
db.update_function_analysis(
    address="00670871",
    opcode_hex="0x09",
    opcode_dec=9,
    function_name="send_attack_opcode_9",
    notes="Attack command with movement and timing",
    ghidra_comment="Sends attack with opcode 0x09 (9). Includes movement, timing validation, and attack processing."
)

# Function 4: Attack JBKK
db.update_function_analysis(
    address="00670a2e",
    opcode_hex="0x1925",
    opcode_dec=6437,
    function_name="send_attack_jbkk_opcode_6437",
    notes="JBKK attack with XOR encryption",
    ghidra_comment="Sends JBKK attack with opcode 0x1925 (6437) and XOR encryption."
)

# Function 5: Skill Use (already renamed)
db.update_function_analysis(
    address="00673fb3",
    opcode_hex="0x08",
    opcode_dec=8,
    function_name="send_skill_use_opcode_8_subcode_281",
    notes="Skill use with subcode 0x119 (281)",
    ghidra_comment="Sends skill use with opcode 0x08 (8) and subcode 0x119 (281)."
)

# Function 6: Character Action (already renamed)
db.update_function_analysis(
    address="00677f70",
    opcode_hex="0x50",
    opcode_dec=80,
    function_name="send_character_action_opcode_80",
    notes="Character action with validation and parameters",
    ghidra_comment="Sends character action with opcode 0x50 (80). Includes validation and parameter processing."
)

# Function 7: UI Action
db.update_function_analysis(
    address="00678048",
    opcode_hex="0x90",
    opcode_dec=144,
    function_name="send_ui_action_opcode_144_90",
    notes="UI action with value 0x5A (90)",
    ghidra_comment="Sends UI action with opcode 0x90 (144) and value 0x5A (90)."
)

# Function 8: Skill Magic UI (already renamed)
db.update_function_analysis(
    address="006789af",
    opcode_hex="0x50",
    opcode_dec=80,
    function_name="handle_skill_magic_ui_opcode_80",
    notes="Skill/magic UI with dual-skill system",
    ghidra_comment="Handles skill/magic UI interactions and casting with opcode 0x50 (80)."
)

# Function 9: Character Action 391
db.update_function_analysis(
    address="00679c45",
    opcode_hex="0x50",
    opcode_dec=80,
    function_name="send_character_action_opcode_80_391_391",
    notes="Character action with codes 0x191/0x187 (401/391)",
    ghidra_comment="Sends character action with opcode 0x50 (80) and actions 0x191/0x187 (401/391)."
)

# Function 10: Character UI 400/390
db.update_function_analysis(
    address="00679f83",
    opcode_hex="0x50",
    opcode_dec=80,
    function_name="handle_character_ui_opcode_80_400_390",
    notes="Character UI with actions 400/390",
    ghidra_comment="Handles character UI with opcode 0x50 (80) and actions 400/390."
)

# Function 11: Character Action Extended (already renamed)
db.update_function_analysis(
    address="0067b315",
    opcode_hex="0x50",
    opcode_dec=80,
    function_name="send_character_action_opcode_80_extended",
    notes="Extended character action with special handling",
    ghidra_comment="Extended character action handler with opcode 0x50 (80) and special action 0x185."
)

# Function 12: Character UI 380
db.update_function_analysis(
    address="0067b621",
    opcode_hex="0x50",
    opcode_dec=80,
    function_name="handle_character_ui_opcode_80_380",
    notes="Character UI with action 0x17C (380)",
    ghidra_comment="Handles character UI with opcode 0x50 (80) and action 0x17C (380)."
)

# Function 13: Attack Extended
db.update_function_analysis(
    address="0067d746",
    opcode_hex="0x09",
    opcode_dec=9,
    function_name="send_attack_opcode_9_extended",
    notes="Extended attack with pet/mount support",
    ghidra_comment="Sends extended attack with opcode 0x09 (9) with pet/mount support."
)

# Function 14: Trade Exchange v2
db.update_function_analysis(
    address="0067ec2b",
    opcode_hex="0x3A",
    opcode_dec=58,
    function_name="handle_trade_exchange_opcode_58_v2",
    notes="Trade/exchange variant 2 with validation",
    ghidra_comment="Handles trade/exchange with opcode 0x3A (58) variant 2 with validation."
)

# Function 15: Data Packet
db.update_function_analysis(
    address="0067f367",
    opcode_hex="0x360",
    opcode_dec=864,
    function_name="send_data_packet_opcode_864",
    notes="Data packet with string copying",
    ghidra_comment="Sends data packet with opcode 0x360 (864) and string copying."
)

# Function 16: Guild Action
db.update_function_analysis(
    address="00680ab7",
    opcode_hex="0x3B3",
    opcode_dec=947,
    function_name="send_guild_action_opcode_947",
    notes="Guild action with string processing",
    ghidra_comment="Sends guild action with opcode 0x3B3 (947) and string processing."
)

# Show updated progress
progress = db.get_progress()
print(f"\n📊 Updated Analysis Progress:")
print(f"Total Functions: {progress['total_functions']}")
print(f"Analyzed: {progress['analyzed_functions']} ({progress['completion_percentage']}%)")
print(f"Pending: {progress['pending_functions']}")

# Export updated data
db.export_to_csv("func_analysis_status_updated.csv")
print("\n✅ Batch 6 completed - 16 functions analyzed!")
