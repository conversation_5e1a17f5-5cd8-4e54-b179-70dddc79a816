#!/usr/bin/env python3
"""
Update batch 4 analysis results in database
"""

from packet_analysis_db import PacketAnalysisDB

# Initialize database
db = PacketAnalysisDB()

print("Updating batch 4 functions...")

# All functions use action 7 with different opcodes in 0x4BB-0x4D5 range (File Management System)

# Function 1: File Action
db.update_function_analysis(
    address="00664020",
    opcode_hex="0x4BD",
    opcode_dec=1213,
    function_name="send_file_action_opcode_1213",
    notes="Basic file operation with parameter",
    ghidra_comment="Sends file action with opcode 0x4BD (1213) and action 7. Basic file operation with parameter."
)

# Function 2: File Request
db.update_function_analysis(
    address="006646f3",
    opcode_hex="0x4BB",
    opcode_dec=1211,
    function_name="send_file_request_opcode_1211",
    notes="File request with parameter",
    ghidra_comment="Sends file request with opcode 0x4BB (1211) and action 7. File request with parameter."
)

# Function 3: File Data Copy
db.update_function_analysis(
    address="006648b2",
    opcode_hex="0x4D5",
    opcode_dec=1237,
    function_name="send_file_data_copy_opcode_1237",
    notes="Large data blocks and string copying",
    ghidra_comment="Sends file data copy with opcode 0x4D5 (1237) and action 7. Copies large data blocks and strings."
)

# Function 4: File Load
db.update_function_analysis(
    address="00664ab9",
    opcode_hex="0x4C7",
    opcode_dec=1223,
    function_name="send_file_load_opcode_1223",
    notes="File loading with path string processing",
    ghidra_comment="Sends file load with opcode 0x4C7 (1223) and action 7. Loads file and processes path strings."
)

# Function 5: File Multipath
db.update_function_analysis(
    address="00664ef0",
    opcode_hex="0x4C3",
    opcode_dec=1219,
    function_name="send_file_multipath_opcode_1219",
    notes="Multiple file paths with conditional logic",
    ghidra_comment="Sends file multipath with opcode 0x4C3 (1219) and action 7. Handles multiple file paths with conditional logic."
)

# Function 6: File Status
db.update_function_analysis(
    address="00665440",
    opcode_hex="0x4C9",
    opcode_dec=1225,
    function_name="send_file_status_opcode_1225",
    notes="File status with value 1",
    ghidra_comment="Sends file status with opcode 0x4C9 (1225) and action 7. File status with value 1."
)

# Function 7: File Transfer
db.update_function_analysis(
    address="0066562f",
    opcode_hex="0x4C5",
    opcode_dec=1221,
    function_name="send_file_transfer_opcode_1221",
    notes="File transfer with two parameters",
    ghidra_comment="Sends file transfer with opcode 0x4C5 (1221) and action 7. File transfer with two parameters."
)

# Function 8: File Close
db.update_function_analysis(
    address="00665b3a",
    opcode_hex="0x4BF",
    opcode_dec=1215,
    function_name="send_file_close_opcode_1215",
    notes="Close file operations with path processing",
    ghidra_comment="Sends file close with opcode 0x4BF (1215) and action 7. Closes file operations with path processing."
)

# Show updated progress
progress = db.get_progress()
print(f"\n📊 Updated Analysis Progress:")
print(f"Total Functions: {progress['total_functions']}")
print(f"Analyzed: {progress['analyzed_functions']} ({progress['completion_percentage']}%)")
print(f"Pending: {progress['pending_functions']}")

# Export updated data
db.export_to_csv("func_analysis_status_updated.csv")
print("\n✅ Batch 4 completed - File Management System discovered!")
