# Phân Tích Hoàn Chỉnh: Luồng Mã Hóa/Giải Mã Packet

## 🔍 **Tổng Quan**

Ứng dụng sử dụng một hệ thống mã hóa đa lớp với các thuật toán khác nhau:
1. **AES CBC** - Mã hóa chính cho dữ liệu packet
2. **RSA** - Mã hóa key exchange và một số packet đặc biệt
3. **Custom Encryption** - Thuật toán tùy chỉnh (chưa tìm thấy hoàn toàn)
4. **LZ77 Compression** - Nén dữ liệu trước khi mã hóa

## 📡 **Luồng Gửi Packet (WSASend)**

### **1. Hàm Chính Gửi Packet**
- **Địa chỉ:** `0x007ac8b0`
- **Tên:** `send_encrypted_packet`
- **WSASend call:** Tại địa chỉ `0x007acb37`

### **2. Chuỗi Mã Hóa Gửi Đi:**

```
Raw Data → Custom Encryption → AES CBC → Add Header/Footer → WSASend
```

**2.1. `send_encrypted_packet` (0x007ac8b0)**
- Tạo header/footer với magic bytes (0xAA, 0x55)
- Gọi `process_packet_encryption` để mã hóa dữ liệu
- Gửi packet qua WSASend

**2.2. `process_packet_encryption` (0x006281d0)**
- Kiểm tra packet type dựa trên magic number
- Gọi `encrypt_packet_wrapper` để thực hiện mã hóa

**2.3. `encrypt_packet_wrapper` (0x00627a90)**
- Wrapper function cho quá trình mã hóa
- Gọi `FUN_00625c70` (Custom encryption - chưa phân tích đầy đủ)
- Gọi `aes_encrypt_packet` để mã hóa AES

**2.4. `aes_encrypt_packet` (0x00627720)**
- **Hàm mã hóa chính sử dụng AES CBC**
- Gọi `AES_set_encrypt_key` để thiết lập key
- Gọi `AES_cbc_encrypt` để mã hóa dữ liệu
- Thêm PKCS7 padding

**2.5. `rsa_encrypt_packet` (0x00628460)**
- Mã hóa RSA cho key exchange và packet đặc biệt

## 📨 **Luồng Nhận Packet (WSARecv)**

### **1. Hàm Chính Nhận Packet**
- **Địa chỉ:** `0x007ac640`
- **Tên:** `receive_data_from_server`
- **WSARecv call:** Được gọi trong hàm này

### **2. Chuỗi Xử Lý Nhận:**

```
WSARecv → Buffer Management → Packet Parsing → Custom Decryption → AES Decryption → LZ77 Decompression → Packet Processing
```

**2.1. `network_event_handler` (0x007ac4e0)**
- Xử lý network events
- Gọi `receive_data_from_server` để nhận dữ liệu
- Gọi `receive_and_parse_packet` để phân tích packet

**2.2. `receive_data_from_server` (0x007ac640)**
- **Gọi WSARecv để nhận dữ liệu từ server**
- Kiểm tra kích thước dữ liệu nhận được
- Gọi `add_data_to_receive_buffer` để thêm vào buffer

**2.3. `receive_and_parse_packet` (0x007abab0)**
- Phân tích cấu trúc packet với magic bytes (0xAA, 0x55)
- Kiểm tra header và footer
- Gọi `add_data_to_receive_buffer` để xử lý dữ liệu

**2.4. `add_data_to_receive_buffer` (0x007ac140)**
- Quản lý buffer nhận dữ liệu
- Sao chép dữ liệu vào buffer

**2.5. `read_from_buffer` (0x007ab0f0)**
- Đọc dữ liệu từ circular buffer
- Xử lý wrap-around của buffer

**2.6. `process_received_data` (0x007ab610)**
- Xử lý dữ liệu đã nhận
- Gọi `decrypt_packet_data` để giải nén LZ77
- Gọi `handle_received_packet` để xử lý từng packet

**2.7. `decompress_packet_lz77` (0x007ad8e0)** ✅ **Đã sửa tên**
- **Thuật toán giải nén LZ77**
- Chỉ được gọi cho packet có opcode 0xc0
- Giải nén dữ liệu đã được nén trước khi mã hóa

**2.8. `handle_received_packet` (0x00e48be0)**
- **Gọi `process_packet_decryption` để giải mã packet**
- Xử lý các loại packet khác nhau dựa trên packet type

### **3. Chuỗi Giải Mã:**

**3.1. `process_packet_decryption` (0x00628130)**
- Kiểm tra packet type dựa trên magic number
- Gọi `decrypt_packet_wrapper` để thực hiện giải mã

**3.2. `decrypt_packet_wrapper` (0x00627520)**
- Wrapper function cho quá trình giải mã
- Gọi `prepare_decryption_buffer` (0x00625c70) để chuẩn bị buffer
- Gọi `aes_decrypt_packet` để giải mã AES

**3.3. `aes_decrypt_packet` (0x00627090)**
- **Hàm giải mã chính sử dụng AES CBC**
- Gọi `AES_set_decrypt_key` để thiết lập key
- Gọi `AES_cbc_encrypt` với mode decrypt
- Xử lý PKCS7 padding removal

## 🔑 **Cấu Trúc Packet**

### **Packet Format:**
```
[0xAA] [0x55] [Length_Low] [Length_High] [Encrypted_Data] [0x55] [0xAA]
```

### **Encrypted Data Structure:**
```
[Custom_Encrypted_Data] → [AES_CBC_Encrypted] → [LZ77_Compressed_Original_Data]
```

## 🔍 **Các Hàm Hỗ Trợ Đã Phân Tích**

### **Buffer Management:**
- `copy_data_from_buffer` (0x007ab030)
- `remove_data_from_buffer` (0x007ab1e0)

### **Validation & Logging:**
- `check_packet_validation` (0x007ab0d0)
- `log_packet_info` (0x007b8f80)
- `dump_packet_data` (0x007acc20)

### **Packet Reading:**
- `read_packet_dword` (0x0061ee90) - Đọc 4 bytes từ packet
- `read_packet_word` (0x0061eeb0) - Đọc 2 bytes từ packet
- `get_packet_handler_table` (0x00628450) - Trả về bảng xử lý packet

### **Decryption Support Functions:**
- `prepare_decryption_buffer` (0x00625c70) - Chuẩn bị buffer cho giải mã
- `setup_decryption_params` (0x00625f90) - Thiết lập tham số giải mã
- `calculate_buffer_size` (0x00626840) - Tính toán kích thước buffer
- `copy_buffer_data` (0x006260c0) - Sao chép dữ liệu buffer
- `copy_data_wrapper` (0x00626170) - Wrapper cho việc sao chép dữ liệu

## ❓ **Thuật Toán Custom Encryption Chưa Tìm Thấy**

Dựa trên mô tả của bạn về thuật toán `ComputeKeySegment`:
```csharp
public static uint ComputeKeySegment(uint value)
{
    var eax = value;
    var ecx = eax ^ 0x3D0000;
    ecx = (ecx >> 16) ^ eax;
    ecx = (ecx + ecx * 8) & 0xFFFFFFFF;
    eax = ecx;
    eax = (eax >> 4) ^ ecx;
    eax = (eax * 0x73C2EB2D) & 0xFFFFFFFF;
    var ebx = eax;
    ebx = (ebx >> 15) ^ eax;
    return ebx;
}
```

**Các hằng số đặc biệt cần tìm:**
- `0x3D0000`
- `0x73C2EB2D`

**Vị trí có thể:**
- Trong `FUN_00625c70` hoặc các hàm liên quan
- Có thể được gọi trước `aes_encrypt_packet` trong `encrypt_packet_wrapper`
- Có thể được gọi sau `aes_decrypt_packet` trong `decrypt_packet_wrapper`

## 📊 **Luồng Hoàn Chỉnh**

### **Gửi:**
```
Application Data → Custom Encryption → AES CBC → Packet Header → WSASend
```

### **Nhận:**
```
WSARecv → Packet Parsing → AES CBC Decrypt → Custom Decrypt → LZ77 Decompress → Application Data
```

## 🔍 **Tiến Trình Tìm Kiếm Custom Encryption**

### **Các Hàm Đã Kiểm Tra:**

**Trong decrypt_packet_wrapper:**
- `prepare_decryption_buffer` (0x00625c70) - Chuẩn bị buffer cho giải mã ✅
- `setup_decryption_params` (0x00625f90) - Thiết lập tham số ✅
- `calculate_buffer_size` (0x00626840) - Tính toán kích thước ✅
- `copy_buffer_data` (0x006260c0) - Sao chép dữ liệu ✅
- `copy_data_wrapper` (0x00626170) - Wrapper sao chép ✅
- `optimized_memcpy` (0x01056200) - Không phải encryption ✅

**Trong handle_received_packet:**
- `read_packet_dword` (0x0061ee90) - Đọc 4 bytes ✅
- `read_packet_word` (0x0061eeb0) - Đọc 2 bytes ✅
- `get_packet_handler_table` (0x00628450) - Bảng xử lý ✅

**Các hàm khác đã kiểm tra:**
- `FUN_00628280` - Random number generator với hằng số `0x6c078965`
- `FUN_00628bb0` - Mersenne Twister RNG
- `FUN_00628450` - Return constant `0x18e0600`

### **Phát Hiện Mới:**

1. **✅ Đã phân tích hoàn chỉnh luồng từ WSARecv đến handle_received_packet**
2. **✅ Đã đổi tên `decrypt_packet_data` thành `decompress_packet_lz77`** - chỉ hoạt động với opcode 0xc0
3. **✅ Đã phân tích các hàm hỗ trợ trong decrypt_packet_wrapper**
4. **✅ Đã phân tích game_main_loop và các hàm liên quan**
5. **✅ Đã phân tích sâu các hàm trong encryption/decryption wrapper**
6. **❌ Không tìm thấy custom encryption với ComputeKeySegment**

### **Các Hàm Mới Đã Phân Tích:**

**Game Main Loop:**
- `game_main_loop` (0x008cd500) - Main game loop, gọi send_encrypted_packet và network_event_handler ✅
- `recover_device_lost` (0x008ccb80) - Khôi phục device lost ✅
- `calculate_fps` (0x008cc8f0) - Tính toán FPS ✅
- `get_performance_timer` (0x00621440) - Timer/performance counter ✅

**Game Systems:**
- `process_input_and_send_packets` (0x007a8c10) - Xử lý input và gửi packets ✅
- `capture_screenshot` (0x007a7de0) - Capture screenshot ✅
- `manage_game_state` (0x0086f060) - Quản lý game state ✅
- `process_game_objects` (0x00e4a060) - Xử lý game objects ✅

**Encryption/Decryption Support:**
- `initialize_buffer` (0x005c9e20) - Khởi tạo buffer ✅
- `cleanup_buffer` (0x005d54c0) - Cleanup buffer ✅
- `debug_log_packet` (0x007acdc0) - Debug logging packet ✅

**Graphics & Rendering:**
- `render_graphics_element` (0x00625930) - Render graphics element ✅
- `get_graphics_data_1` (0x00625bf0) - Get graphics data ✅
- `get_graphics_data_2` (0x00625c30) - Get graphics data ✅
- `empty_function` (0x00610130) - Empty function ✅
- `render_3d_object` (0x00610140) - Render 3D object ✅
- `process_3d_rendering` (0x00610920) - Process 3D rendering ✅
- `graphics_check_status` (0x00500036) - Graphics status check ✅
- `graphics_set_flag` (0x0050004b) - Graphics flag setting ✅
- `graphics_set_color` (0x0050008f) - Graphics color setting ✅

**Buffer & Memory Management:**
- `post_aes_buffer_copy` (0x00626bd0) - Copy buffer after AES ✅
- `buffer_management_copy` (0x00625d00) - Buffer management copy ✅
- `copy_data_to_buffer` (0x00626120) - Copy data to buffer ✅
- `copy_and_advance_pointer` (0x00626950) - Copy and advance pointer ✅
- `validate_buffer_capacity` (0x005d2400) - Validate buffer capacity ✅
- `allocate_buffer_memory` (0x005d2430) - Allocate buffer memory ✅
- `allocate_memory_block` (0x005d5ee0) - Allocate memory block ✅

**Encryption/Decryption Chain:**
- `pre_aes_encryption_setup` (0x00626c50) - Pre-AES setup (⚠️ **Potential custom encryption**) ✅
- `setup_encryption_buffer` (0x00625ec0) - Setup encryption buffer (⚠️ **Potential custom encryption**) ✅

**🎉 CUSTOM ENCRYPTION ALGORITHM (FOUND!):**
- `ComputeKeySegment` (0x007b9160) - **Core key computation algorithm** ✅
- `custom_encrypt_decrypt_data` (0x007b9090) - **XOR encryption with computed key** ✅
- `custom_encrypt_decrypt_data_alt` (0x007b8fc0) - **Alternative XOR encryption** ✅
- `encrypt_packet_with_custom_key` (0x007b8f10) - **Packet encryption with random key** ✅
- `decrypt_packet_with_custom_key` (0x007b8eb0) - **Packet decryption with header key** ✅
- `custom_packet_crypto_handler` (0x007b8f80) - **Main crypto handler (0=encrypt, 1=decrypt)** ✅

### **Quan Sát Quan Trọng:**

1. **Cả send và recv đều sử dụng cùng chuỗi hàm:**
   - `prepare_decryption_buffer` → `aes_encrypt/decrypt_packet`
   - Điều này cho thấy custom encryption có thể nằm trong `prepare_decryption_buffer`

2. **Luồng mã hóa/giải mã đối xứng:**
   - **Gửi:** Raw Data → Custom Encryption → AES CBC → Send
   - **Nhận:** Recv → AES CBC → Custom Decryption → Raw Data

### **Phân Tích Sâu Hoàn Thành:**

1. **✅ Đã phân tích toàn bộ chuỗi `prepare_decryption_buffer`:**
   - `prepare_decryption_buffer` → `setup_decryption_params` → `calculate_buffer_size` → `copy_buffer_data` → `copy_data_wrapper` → `optimized_memcpy`
   - **Kết luận:** Chỉ là buffer management, không có custom encryption

2. **✅ Đã phân tích các hàm trong AES encryption:**
   - `FUN_00626c50`, `FUN_00626270`, `FUN_00626340` - Chỉ là buffer management
   - `FUN_00626240` - memset, `FUN_005c8950` - memset
   - **Kết luận:** Không có custom encryption trong AES functions

3. **✅ Đã phân tích chi tiết luồng từ `network_event_handler`:**
   - `network_event_handler` → `receive_data_from_server` → `receive_and_parse_packet` → `read_from_buffer` → `process_received_data` → `handle_received_packet` → `process_packet_decryption` → `decrypt_packet_wrapper` → `prepare_decryption_buffer` → `aes_decrypt_packet` → `post_aes_buffer_copy`
   - **Kết luận:** Toàn bộ chuỗi chỉ là buffer management và AES decryption

4. **✅ Đã phân tích chi tiết luồng từ `send_encrypted_packet`:**
   - `send_encrypted_packet` → `process_packet_encryption` → `encrypt_packet_wrapper` → `prepare_decryption_buffer` → `aes_encrypt_packet` → `pre_aes_encryption_setup` → `setup_encryption_buffer`
   - **Phát hiện:** Custom encryption có thể nằm trong `pre_aes_encryption_setup` hoặc `setup_encryption_buffer`

4. **✅ Đã phân tích các hàm support:**
   - `initialize_buffer`, `cleanup_buffer` - Buffer management
   - `debug_log_packet` - Debug logging
   - `validate_buffer_capacity`, `allocate_buffer_memory`, `allocate_memory_block` - Memory management
   - **Kết luận:** Không có custom encryption

### **Kết Luận Cuối Cùng:**

**❌ Không tìm thấy custom encryption algorithm với ComputeKeySegment**

**Khả năng:**
1. **Client đã thay đổi hoàn toàn thuật toán mã hóa** - không còn sử dụng ComputeKeySegment
2. **Custom encryption đã bị loại bỏ** - chỉ sử dụng AES CBC + RSA
3. **Thuật toán được implement ở level khác** - có thể trong driver hoặc hardware level

### **Đặc Điểm Thuật Toán ComputeKeySegment:**
- Sử dụng XOR với `0x3D0000`
- Shift right 16 bit và 4 bit
- Nhân với `0x73C2EB2D`
- Shift right 15 bit
- Các phép toán bit phức tạp

## 🎯 **Kết Luận Cuối Cùng**

### **✅ Đã Hoàn Thành Toàn Bộ Phân Tích:**

1. **Phân tích hoàn chỉnh luồng WSARecv → handle_received_packet**
2. **Phân tích hoàn chỉnh luồng game_main_loop → send_encrypted_packet**
3. **Đặt tên và phân loại 50+ hàm trong chuỗi xử lý**
4. **Phân tích sâu từng hàm một trong encryption/decryption chain**

### **🔍 Hệ Thống Mã Hóa Được Xác Định:**

1. **AES CBC encryption/decryption** - Mã hóa chính cho packet data
2. **RSA encryption** - Key exchange và packet đặc biệt
3. **LZ77 compression/decompression** - Chỉ cho opcode 0xc0
4. **Packet format:** `[0xAA][0x55][Length][Data][0x55][0xAA]`
5. **Buffer management system** - Circular buffer với validation

### **🎉 ĐÃ TÌM THẤY CUSTOM ENCRYPTION ALGORITHM!**

**✅ Đã xác định được ComputeKeySegment và custom encryption:**

**1. ComputeKeySegment Algorithm (0x007b9160):**
```c
uint ComputeKeySegment(uint param_1) {
    uint uVar1 = (param_1 ^ 0x3d ^ param_1 >> 0x10) * 9;
    uVar1 = (uVar1 >> 4 ^ uVar1) * 0x73c2eb2d;
    return uVar1 >> 0xf ^ uVar1;
}
```

**2. Custom Encryption Functions:**
- `custom_encrypt_decrypt_data` (0x007b9090) - XOR data với key từ ComputeKeySegment
- `custom_encrypt_decrypt_data_alt` (0x007b8fc0) - Alternative implementation
- `encrypt_packet_with_custom_key` (0x007b8f10) - Tạo random key, encrypt, thêm key vào header
- `decrypt_packet_with_custom_key` (0x007b8eb0) - Lấy key từ header, decrypt
- `custom_packet_crypto_handler` (0x007b8f80) - Main handler (0=encrypt, 1=decrypt)

**3. Vị Trí Trong Luồng Xử Lý:**

**Luồng Gửi (Send):**
- `send_encrypted_packet` → ... → **`custom_packet_crypto_handler(0, data, size)`** → Tạo packet header

**Luồng Nhận (Receive):**
- `receive_and_parse_packet` → Parse header → **`custom_packet_crypto_handler(1, data, size)`** → `add_data_to_receive_buffer`

**🔑 Custom encryption được thực hiện TRƯỚC AES CBC, không phải trong AES chain!**

### **� Kết Luận Kỹ Thuật:**

**✅ ĐÃ TÌM THẤY HOÀN CHỈNH CUSTOM ENCRYPTION:**

1. **ComputeKeySegment Algorithm:** Sử dụng hằng số `0x3d` và `0x73c2eb2d` với các phép toán XOR, shift, multiply
2. **Custom Encryption:** XOR data với key được tính từ ComputeKeySegment
3. **Integration:** Custom encryption được thực hiện TRƯỚC AES CBC trong cả luồng gửi và nhận
4. **Key Management:** Random key cho mỗi packet, được lưu trong packet header

**Hệ thống bảo mật hoàn chỉnh:** **Custom XOR Encryption + AES CBC + RSA + LZ77 + Packet validation**

**🎯 Vị trí chính xác:**
- **Send:** `send_encrypted_packet` → `custom_packet_crypto_handler(0)` → AES → WSASend
- **Receive:** WSARecv → Parse → `custom_packet_crypto_handler(1)` → AES → Handle packet

---

## 📋 **OPCODE HANDLERS ANALYSIS**

### **✅ Đã phân tích và đổi tên các Opcode Handlers:**

**Small Opcodes (< 0x212):**
- **Opcode 6 (0x06):** `handle_opcode_6_game_start_result` - Xử lý kết quả game start, gửi response packets (0x1606, 0x211, 0x54d)
- **Opcode 10 (0x0A):** Character login validation - Kiểm tra login state và xử lý character selection
- **Opcode 12 (0x0C):** Character login validation (alternative) - Tương tự opcode 10
- **Opcode 32 (0x20):** `handle_opcode_32_character_sync` - Đồng bộ character với server time và character data
- **Opcode 36 (0x24):** `handle_opcode_36_character_action` - Xử lý actions/emotes phức tạp (0x96-0xdf) với sounds và animations
- **Opcode 87 (0x57):** `handle_opcode_87_logout_disconnect` - Logout/disconnect, cleanup toàn bộ game state và objects
- **Opcode 96 (0x60):** `handle_opcode_96_object_interaction` - Tương tác với objects, tìm object theo ID và gọi method
- **Opcode 105 (0x69):** `handle_opcode_105_107_character_movement` - Cập nhật vị trí/movement của character
- **Opcode 106 (0x6A):** `handle_opcode_105_107_character_movement` - Cập nhật vị trí/movement của character
- **Opcode 107 (0x6B):** `handle_opcode_105_107_character_movement` - Cập nhật vị trí/movement của character
- **Opcode 113 (0x71):** `handle_opcode_113_character_state_stub` - Character state update (stub function)
- **Opcode 119 (0x77):** `handle_opcode_119_126_127_character_animation` - Animation phức tạp với item/skill effects
- **Opcode 126 (0x7E):** `handle_opcode_119_126_127_character_animation` - Animation phức tạp với item/skill effects
- **Opcode 127 (0x7F):** `handle_opcode_119_126_127_character_animation` - Animation phức tạp với item/skill effects
- **Opcode 128 (0x80):** `handle_opcode_128_character_status` - Cập nhật character status, buffs/debuffs, UI updates
- **Opcode 141 (0x8D):** `handle_opcode_141_equipment_inventory` - Cập nhật equipment/inventory với sound effects

**Large Opcodes (> 0x212):**
- **Opcode 530 (0x212):** `handle_opcode_530_character_info` - Xử lý thông tin character chi tiết (stats, position, equipment data)
- **Opcode 933 (0x3A5):** Object/NPC interaction và data processing - Xử lý tương tác với NPCs và objects phức tạp
- **Opcode 934 (0x3A6):** Object/NPC interaction cleanup - Dọn dẹp sau khi tương tác với objects/NPCs
- **Opcode 1245 (0x4DD):** `handle_opcode_1245_castle_item_effects` - Xử lý bomb và repair effects trên castle objects
- **Opcode 5636 (0x1604):** `handle_opcode_5636_find_nearby_objects` - Tìm objects trong radius, trả về data objects gần đó
- **Opcode 6360 (0x18D8):** Message/chat system processing - Xử lý hệ thống chat và message với encryption
- **Opcode 8209 (0x2011):** System information exchange - Trao đổi thông tin hệ thống và metrics

### **🎯 Đặc điểm chung của Opcode Handlers:**

1. **Validation:** Tất cả handlers đều kiểm tra object existence và permissions
2. **Object Management:** Sử dụng `find_object_by_id_in_table` để tìm objects
3. **Sound Effects:** Nhiều handlers gọi sound effects thông qua `FUN_00a58f50`
4. **Visual Effects:** Các handlers animation gọi visual effects qua `FUN_009a3250`
5. **Error Handling:** Sử dụng exception handling và memory management
6. **State Management:** Cập nhật game state và character properties
7. **Network Response:** Một số handlers gửi response packets về server
