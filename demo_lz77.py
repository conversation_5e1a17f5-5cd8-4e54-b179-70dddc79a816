#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Demo script cho LZ77 Decompressor
Sử dụng để test và demo việc giải nén packet data
"""

from lz77_decompressor import decompress_lz77_packet_data, decompress_packet_from_hex


def demo_basic_usage():
    """Demo cách sử dụng cơ bản"""
    print("=== DEMO CƠ BẢN ===")

    # Test với literal data
    print("\n1. Test với literal data:")
    literal_data = bytes([0x04, 0x48, 0x65, 0x6C, 0x6C, 0x6F])  # "Hello"
    result = decompress_lz77_packet_data(literal_data)
    print(f"Input:  {literal_data.hex()}")
    print(f"Output: {result}")
    print(f"Text:   {result.decode('ascii', errors='ignore')}")

    # Test với hex string
    print("\n2. Test với hex string:")
    hex_data = "02414243"  # Literal "ABC"
    result = decompress_packet_from_hex(hex_data)
    print(f"Hex:    {hex_data}")
    print(f"Output: {result}")
    print(f"Text:   {result.decode('ascii', errors='ignore')}")


def demo_with_your_data():
    """
    Hàm này để bạn test với dữ liệu thực tế của mình
    Thay thế hex_string bên dưới bằng dữ liệu packet của bạn
    """
    print("\n=== TEST VỚI DỮ LIỆU CỦA BẠN ===")

    # Thay thế chuỗi hex này bằng dữ liệu packet thực tế của bạn
    your_hex_data = "0D AB 05 00 00 67 00 14 04 0D 00 00 00 4B 32 80 03 06 EB 3A 01 00 E4 0C 00 60 03 0A 00 10 75 C5 00 00 70 41 00 10 11 20 07 09 80 40 CE 43 2E 3F DB 87 3B 3F E0 05 17 E0 03 00 00 34 60 0C 00 FF 20 00 00 B0 20 4F 40 03 00 F1 20 4F 01 10 0E 20 4A 20 03 02 00 40 71 20 47 20 4F 01 80 0E 20 07 09 80 40 17 3D 5F 3F 76 9A FA BE E0 05 17 E0 03 00 00 36 60 0C 20 4E 01 FF 4A 20 4F 40 03 E0 04 9F 01 F0 79 20 47 20 4F 01 A0 06 20 07 09 80 40 E3 27 40 3F B8 26 29 BF E0 05 17 E0 03 00 E0 01 9F 00 8B 20 4F 40 03 00 EF 20 9F 00 A6 20 9B 40 03 02 00 F0 73 20 47 20 4F 01 00 01 20 07 09 80 40 6B 5E 03 3F 1C B9 5B 3F E0 05 17 E0 03 00 00 37 60 0C 40 9F 00 8C 20 4F 40 03 E0 04 4F 00 D0 A0 EF 01 30 02 60 4F 09 93 00 95 BD 52 52 7F 3F 00 B0 A1 57 02 80 FF C4 60 40 E0 00 00 E0 01 4F 00 8F 20 4F 40 03 E0 05 4F 00 6F 20 47 40 9F 00 0B 20 07 0A 80 40 CA B0 0F 3F CB DE 53 3F 00 E0 04 17 E0 03 00 E0 01 4F 00 90 20 4F 40 03 E0 03 4F 01 6E A1 80 4F 01 D5 21 80 4F 09 EC 05 FE BE 2F 45 5E BF 00 90 A0 B7 01 A0 0C 20 67 E0 03 00 E0 01 4F 00 AA 20 4F 40 03 E1 04 DF 01 A0 78 20 2F 20 9F 00 B0 81 3F 07 9F 3A 78 3F 33 63 7A 3E E0 05 17 E0 03 00 E1 01 DF 00 AF 20 4F 40 03 E0 04 4F 01 00 5A 80 4F 00 A0 80 9F 0A 6D 5A F5 BE 20 B1 60 BF 00 20 58 80 17 00 C0 42 47 E0 03 00 E0 01 4F 00 8D 20 4F 40 03 E0 03 EF 01 00 B0 A0 37 00 50 40 37 09 80 40 7E F6 03 3F E8 5D 5B BF E0 05 17 E0 03 00 E0 01 EF 00 84 20 4F 40 03 E0 04 4F 01 C0 48 80 87 01 00 FA 21 C7 09 80 40 67 8F 61 BF 3E 25 F2 3E E0 05 17 E0 03 00 E0 01 4F 00 8E 20 4F 40 03 E0 03 4F 02 5D 17 56 60 4F 01 A8 0E 80 EF 09 54 84 A9 BE C5 8F 71 3F 00 60 80 17 02 00 20 0A 20 1F E0 03 00 E0 01 4F 00 AE 20 4F 40 03 E1 04 3F 01 A0 57 20 2F 21 8F 00 90 41 C7 09 80 40 6C 40 E6 BE 16 A7 64 BF E0 05 17 E0 03 00 C1 3F 01 FF FF".replace(' ', '')  # Ví dụ: literal "ABC"

    try:
        print(f"Dữ liệu hex: {your_hex_data}")
        result = decompress_packet_from_hex(your_hex_data)
        print(f"Kết quả:     {result}")
        print(f"Hex output:  {result.hex()}")

        # Thử decode thành text nếu có thể
        try:
            text = result.decode('utf-8')
            print(f"Text:        {text}")
        except UnicodeDecodeError:
            try:
                text = result.decode('ascii', errors='ignore')
                print(f"ASCII:       {text}")
            except:
                print("Không thể decode thành text")

        print(f"Độ dài:      {len(result)} bytes")

    except Exception as e:
        print(f"Lỗi: {e}")


def interactive_mode():
    """Chế độ tương tác để test nhiều dữ liệu"""
    print("\n=== CHẾ ĐỘ TƯƠNG TÁC ===")
    print("Nhập dữ liệu hex để giải nén (hoặc 'quit' để thoát)")

    while True:
        try:
            hex_input = input("\nNhập hex data: ").strip()
            if hex_input.lower() in ['quit', 'exit', 'q']:
                break

            if not hex_input:
                continue

            result = decompress_packet_from_hex(hex_input)
            print(f"Kết quả: {result}")
            print(f"Hex:     {result.hex()}")

            # Thử decode
            try:
                text = result.decode('utf-8')
                print(f"Text:    {text}")
            except:
                try:
                    text = result.decode('ascii', errors='ignore')
                    print(f"ASCII:   {text}")
                except:
                    print("Không decode được thành text")

        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Lỗi: {e}")

    print("Thoát chế độ tương tác.")


if __name__ == "__main__":
    print("LZ77 Packet Decompressor Demo")
    print("=" * 40)

    demo_basic_usage()
    demo_with_your_data()

    # Uncomment dòng dưới để chạy chế độ tương tác
    # interactive_mode()