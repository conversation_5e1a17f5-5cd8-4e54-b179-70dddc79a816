# Packet Analysis Summary

## Main Function
- **send_packet_with_header_encryption** (0x007ac8b0)
  - Original name: `send_encrypted_packet`
  - Function: Main packet sending function with encryption header format `[0xAA][0x55][Length][Data][0x55][0xAA]`
  - Variables renamed:
    - `param_1` → `packet_data`
    - `length` → `packet_length`
    - `local_2008` → `encrypted_packet_buffer`
    - `local_4008` → `temp_buffer`
    - `local_400c` → `buffer_offset`

## Functions by Opcode

### Authentication & Connection
- **send_login_auth_opcode_218** (0x00591c90)
  - Opcode: 0xDA (218)
  - Function: Login authentication with log file creation
  - Original name: `FUN_00591c90`

- **send_disconnect_opcode_2** (0x007a44d0)
  - Opcode: 0x02 (2)
  - Function: Disconnect/logout packet
  - Original name: `FUN_007a44d0`

### Game State & Movement
- **send_game_start_opcode_5** (0x00597f00)
  - Opcode: 0x05 (5)
  - Function: Game start initialization
  - Original name: `FUN_00597f00`

- **send_movement_opcode_29_or_7** (0x0083e670)
  - Opcode: 0x1D (29) or 0x07 (7)
  - Function: Character movement packets
  - Original name: `FUN_0083e670`

- **send_world_update_opcode_224** (0x0059da00)
  - Opcode: 0xE0 (224)
  - Function: World state updates
  - Original name: `FUN_0059da00`

### Character & Inventory
- **handle_character_info_opcode_530** (0x00e426b3)
  - Opcode: 0x530 (1328)
  - Function: Character information handling
  - Original name: `handleOpcode530`

- **send_character_spawn_opcode_20759** (0x00593d00)
  - Opcode: 0x5117 (20759)
  - Function: Character spawn in world
  - Original name: `FUN_00593d00`

- **send_character_spawn_opcode_20758** (0x00594f60)
  - Opcode: 0x5116 (20758)
  - Function: Character spawn (alternative)
  - Original name: `FUN_00594f60`

- **send_inventory_update_opcode_12582** (0x00b34a40)
  - Opcode: 0x3126 (12582)
  - Function: Inventory updates and item management
  - Original name: `FUN_00b34a40`

### Items & Actions
- **send_item_drop_opcode_60** (0x008634b0)
  - Opcode: 0x3C (60)
  - Function: Item drop action
  - Original name: `FUN_008634b0`

- **send_clear_inventory_opcode_16** (0x00c6b080)
  - Opcode: 0x10 (16)
  - Function: Clear inventory slots
  - Original name: `FUN_00c6b080`

### Security & Anti-cheat
- **send_anticheat_check_opcode_22** (0x00c099e0)
  - Opcode: 0x16 (22)
  - Function: Anti-cheat validation and security checks
  - Original name: `FUN_00c099e0`

- **process_input_and_send_screenshot_opcode_905** (0x007a8c10)
  - Opcode: 0x389 (905)
  - Function: Process input and send screenshot for anti-cheat
  - Original name: `process_input_and_send_packets`

### System & Server
- **game_main_loop_send_device_lost_opcode_143** (0x008cd500)
  - Opcode: 0x8F (143)
  - Function: Main game loop, sends device lost packets
  - Original name: `game_main_loop`

- **send_server_unity_opcode_5684** (0x00c76cd0)
  - Opcode: 0x1634 (5684)
  - Function: Server unity/backup functionality
  - Original name: `FUN_00c76cd0`

- **send_request_opcode_384** (0x00e14f10)
  - Opcode: 0x180 (384)
  - Function: General request packet
  - Original name: `FUN_00e14f10`

- **send_zone_request_opcode_6326** (0x00573630)
  - Opcode: 0x18B6 (6326)
  - Function: Zone request packet, sends 12-byte packet with zone information
  - Original name: `FUN_00573630`

### Game Engine & System
- **process_input_and_send_screenshot_opcode_905** (0x007a8c10)
  - Opcode: 0x389 (905)
  - Function: Process input and send screenshot data
  - Original name: `FUN_007a8c10`

- **game_main_loop_send_device_lost_opcode_143** (0x008cd500)
  - Opcode: 0x8F (143)
  - Function: Main game loop with device lost handling
  - Original name: `FUN_008cd500`

- **send_inventory_update_opcode_12582** (0x00b34a40)
  - Opcode: 0x3126 (12582)
  - Function: Update inventory data
  - Original name: `FUN_00b34a40`

- **send_item_drop_opcode_60** (0x008634b0)
  - Opcode: 0x3C (60)
  - Function: Drop item from inventory
  - Original name: `FUN_008634b0`

- **send_anticheat_check_opcode_22** (0x00c099e0)
  - Opcode: 0x16 (22)
  - Function: Anti-cheat validation and checks
  - Original name: `FUN_00c099e0`

- **send_clear_inventory_opcode_16** (0x00c6b080)
  - Opcode: 0x10 (16)
  - Function: Clear inventory
  - Original name: `FUN_00c6b080`

### Combat & Skills
- **send_attack_opcode_9** (0x00670660)
  - Opcode: 0x09 (9)
  - Function: Send attack command
  - Original name: `FUN_00670660`

- **send_skill_use_opcode_8_subcode_281** (0x00673ef0)
  - Opcode: 0x08 (8), Subcode: 0x119 (281)
  - Function: Use skill/ability
  - Original name: `FUN_00673ef0`

### Character Actions
- **send_character_select_opcode_80_subcode_309** (0x00648540)
  - Opcode: 0x50 (80), Subcode: 0x135 (309)
  - Function: Character selection
  - Original name: `FUN_00648540`

- **send_character_action_opcode_80** (0x00677d90)
  - Opcode: 0x50 (80)
  - Function: General character actions
  - Original name: `FUN_00677d90`

### Party & Social
- **send_party_list_opcode_668** (0x006b1380)
  - Opcode: 0x29C (668)
  - Function: Send party member list
  - Original name: `FUN_006b1380`

## Packet Format
All packets use the encryption header format:
```
[0xAA][0x55][Length_Low][Length_High][Data...][0x55][0xAA]
```

### Guild & Social Features
- **send_guild_action_opcode_20752** (0x00718d30)
  - Opcode: 0x5110 (20752)
  - Function: Guild-related actions and management
  - Original name: `FUN_00718d30`

### UI & Interface
- **send_ui_request_opcode_144** (0x00725950)
  - Opcode: 0x90 (144)
  - Function: UI request and interface interactions
  - Original name: `FUN_00725950`

### NPC Interaction
- **send_npc_interaction_opcode_8724** (0x0072c0d0)
  - Opcode: 0x2214 (8724)
  - Function: NPC interaction and dialogue
  - Original name: `FUN_0072c0d0`

### Raid & Dungeon System
- **handle_raid_dungeon_ui_opcode_1280** (0x00749720)
  - Opcode: 0x500 (1280)
  - Function: Raid/dungeon UI handler with multiple subcodes
  - Original name: `FUN_00749720`

- **send_raid_party_action_opcode_1280** (0x007502e0)
  - Opcode: 0x500 (1280)
  - Function: Raid party actions and coordination
  - Original name: `FUN_007502e0`

### Data Management
- **send_reset_data_opcode_8480** (0x00757a90)
  - Opcode: 0x2120 (8480)
  - Function: Reset game data and clear memory
  - Original name: `FUN_00757a90`

### Advanced Movement
- **send_movement_position_opcode_71_or_9** (0x0083c7b0)
  - Opcode: 0x47 (71) or 0x09 (9)
  - Function: Advanced movement with position data and validation
  - Original name: `FUN_0083c7b0`

### Status & State Management
- **send_status_change_opcode_4162** (0x00875e00)
  - Opcode: 0x1042 (4162)
  - Function: Status change notifications
  - Original name: `FUN_00875e00`

### Item Management
- **send_item_action_opcode_1321** (0x00884140)
  - Opcode: 0x529 (1321)
  - Function: Item action and manipulation
  - Original name: `FUN_00884140`

### Communication & Chat
- **send_chat_message_opcode_8** (0x00c9d5d0)
  - Opcode: 0x08 (8)
  - Function: Chat message sending with text validation and formatting
  - Original name: `FUN_00c9d5d0`

### Web Browser & UI
- **handle_web_browser_ui_opcode_212** (0x00ca5390)
  - Opcode: 0xD4 (212)
  - Function: Web browser UI management and file system operations
  - Original name: `FUN_00ca5390`

- **send_ui_request_opcode_144_v2** (0x00cbede0)
  - Opcode: 0x90 (144)
  - Function: Alternative UI request handler with different validation
  - Original name: `FUN_00cbede0`

### Trading & Exchange
- **handle_trade_exchange_opcode_58** (0x00781fc0)
  - Opcode: 0x3A (58)
  - Function: Trade/exchange system with validation and state management
  - Original name: `FUN_00781fc0`

### Character Data Management
- **send_character_data_opcode_12352** (0x00a7b480)
  - Opcode: 0x3040 (12352)
  - Function: Comprehensive character data transmission (0x768 bytes)
  - Original name: `FUN_00a7b480`

- **send_character_action_opcode_80_152** (0x00784150)
  - Opcode: 0x50 (80), Action: 0x98 (152)
  - Function: Character action with specific action code
  - Original name: `FUN_00784150`

### System & Validation
- **send_system_packet_opcode_255** (0x00a95090)
  - Opcode: 0xFF (255)
  - Function: System-level communication and status updates
  - Original name: `FUN_00a95090`

- **send_position_validation_opcode_6000** (0x00aa8dc0)
  - Opcode: 0x1770 (6000)
  - Function: Position validation and anti-cheat checks
  - Original name: `FUN_00aa8dc0`

## Analysis Notes
- Total functions analyzed: 56
- Main packet sending function identified and variables renamed for clarity
- Opcodes range from simple (2) to complex (20759)
- Functions cover authentication, movement, inventory, anti-cheat, server communication, zone management, game engine, combat, skills, character actions, party/social features, guild management, UI interactions, NPC dialogue, raid/dungeon systems, data management, and advanced movement
- All functions use the same encryption wrapper through `send_packet_with_header_encryption`
