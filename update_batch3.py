#!/usr/bin/env python3
"""
Update batch 3 analysis results in database
"""

from packet_analysis_db import PacketAnalysisDB

# Initialize database
db = PacketAnalysisDB()

print("Updating batch 3 functions...")

# All functions use opcode 0x145 (325) with different action codes

# Function 1: Item Action 8
db.update_function_analysis(
    address="00657fe9",
    opcode_hex="0x145",
    opcode_dec=325,
    function_name="send_item_action_opcode_325_action_8",
    notes="Item validation and processing with range checks",
    ghidra_comment="Sends item action with opcode 0x145 (325) and action 8/1. Handles item validation and processing with range checks."
)

# Function 2: Item Search 16
db.update_function_analysis(
    address="00658c12",
    opcode_hex="0x145",
    opcode_dec=325,
    function_name="send_item_search_opcode_325_action_16",
    notes="Item search with string processing and filtering",
    ghidra_comment="Sends item search with opcode 0x145 (325) and action 16/15. Processes search strings and item filtering."
)

# Function 3: Item List 9
db.update_function_analysis(
    address="00658e78",
    opcode_hex="0x145",
    opcode_dec=325,
    function_name="send_item_list_opcode_325_action_9",
    notes="Item list iteration and batch data sending",
    ghidra_comment="Sends item list with opcode 0x145 (325) and action 9/2. Iterates through item list and sends batch data."
)

# Function 4: Item Select 12
db.update_function_analysis(
    address="006591bc",
    opcode_hex="0x145",
    opcode_dec=325,
    function_name="send_item_select_opcode_325_action_12",
    notes="Item selection and validation",
    ghidra_comment="Sends item selection with opcode 0x145 (325) and action 12/5. Handles item selection and validation."
)

# Function 5: Item Close 14
db.update_function_analysis(
    address="006592ac",
    opcode_hex="0x145",
    opcode_dec=325,
    function_name="send_item_close_opcode_325_action_14",
    notes="Close item interface or transaction",
    ghidra_comment="Sends item close with opcode 0x145 (325) and action 14/7. Closes item interface or transaction."
)

# Function 6: Item Search Request 10
db.update_function_analysis(
    address="006596ad",
    opcode_hex="0x145",
    opcode_dec=325,
    function_name="send_item_search_request_opcode_325_action_10",
    notes="Category-based item searching with debug logging",
    ghidra_comment="Sends item search request with opcode 0x145 (325) and action 10/3. Handles category-based item searching with debug logging."
)

# Function 7: Item Buy 13
db.update_function_analysis(
    address="00659a06",
    opcode_hex="0x145",
    opcode_dec=325,
    function_name="send_item_buy_opcode_325_action_13",
    notes="Item purchase transactions",
    ghidra_comment="Sends item buy with opcode 0x145 (325) and action 13/6. Handles item purchase transactions."
)

# Function 8: Item Cancel 11
db.update_function_analysis(
    address="00659aeb",
    opcode_hex="0x145",
    opcode_dec=325,
    function_name="send_item_cancel_opcode_325_action_11",
    notes="Cancel item operations or transactions",
    ghidra_comment="Sends item cancel with opcode 0x145 (325) and action 11/4. Cancels item operations or transactions."
)

# Show updated progress
progress = db.get_progress()
print(f"\n📊 Updated Analysis Progress:")
print(f"Total Functions: {progress['total_functions']}")
print(f"Analyzed: {progress['analyzed_functions']} ({progress['completion_percentage']}%)")
print(f"Pending: {progress['pending_functions']}")

# Export updated data
db.export_to_csv("func_analysis_status_updated.csv")
print("\n✅ Batch 3 completed and exported to func_analysis_status_updated.csv")
