#!/usr/bin/env python3
"""
Packet Analysis Database Manager
Manages SQLite database for tracking packet analysis progress
"""

import sqlite3
import csv
import os
from typing import List, Dict, Optional, Tuple

class PacketAnalysisDB:
    def __init__(self, db_path: str = "packet_analysis.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """Initialize the database and create tables"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Create main analysis table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS packet_functions (
                address TEXT PRIMARY KEY,
                label TEXT,
                code_unit TEXT,
                context TEXT,
                analysis_status TEXT DEFAULT 'PENDING',
                opcode_hex TEXT,
                opcode_dec INTEGER,
                function_name TEXT,
                notes TEXT,
                analyzed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ghidra_comment TEXT
            )
        ''')
        
        # Create analysis progress table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS analysis_progress (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                total_functions INTEGER,
                analyzed_functions INTEGER,
                pending_functions INTEGER,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def import_from_func_file(self, func_file_path: str):
        """Import all function addresses from the func file"""
        if not os.path.exists(func_file_path):
            print(f"Error: {func_file_path} not found")
            return
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        imported_count = 0
        with open(func_file_path, 'r', encoding='utf-8') as f:
            csv_reader = csv.reader(f)
            for row in csv_reader:
                if len(row) >= 4:
                    address = row[0].strip('"')
                    label = row[1].strip('"')
                    code_unit = row[2].strip('"')
                    context = row[3].strip('"')
                    
                    # Insert or ignore if already exists
                    cursor.execute('''
                        INSERT OR IGNORE INTO packet_functions 
                        (address, label, code_unit, context) 
                        VALUES (?, ?, ?, ?)
                    ''', (address, label, code_unit, context))
                    imported_count += 1
        
        conn.commit()
        conn.close()
        print(f"Imported {imported_count} function addresses")
        self.update_progress()
    
    def import_analyzed_data(self, csv_file_path: str):
        """Import already analyzed data from CSV"""
        if not os.path.exists(csv_file_path):
            print(f"Warning: {csv_file_path} not found, skipping import")
            return
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        updated_count = 0
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            csv_reader = csv.reader(f)
            header = next(csv_reader)  # Skip header
            
            for row in csv_reader:
                if len(row) >= 9:
                    opcode_hex = row[0].strip('"')
                    opcode_dec = row[1].strip('"')
                    function_name = row[2].strip('"')
                    address = row[3].strip('"')
                    label = row[4].strip('"')
                    code_unit = row[5].strip('"')
                    context = row[6].strip('"')
                    notes = row[7].strip('"')
                    analysis_status = row[8].strip('"')
                    
                    cursor.execute('''
                        UPDATE packet_functions 
                        SET opcode_hex = ?, opcode_dec = ?, function_name = ?, 
                            notes = ?, analysis_status = ?
                        WHERE address = ?
                    ''', (opcode_hex, opcode_dec, function_name, notes, analysis_status, address))
                    updated_count += 1
        
        conn.commit()
        conn.close()
        print(f"Updated {updated_count} analyzed functions")
        self.update_progress()
    
    def get_pending_functions(self, limit: int = 10) -> List[Dict]:
        """Get pending functions for analysis"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT address, label, code_unit, context 
            FROM packet_functions 
            WHERE analysis_status = 'PENDING' 
            ORDER BY address 
            LIMIT ?
        ''', (limit,))
        
        results = []
        for row in cursor.fetchall():
            results.append({
                'address': row[0],
                'label': row[1],
                'code_unit': row[2],
                'context': row[3]
            })
        
        conn.close()
        return results
    
    def update_function_analysis(self, address: str, opcode_hex: str, opcode_dec: int, 
                               function_name: str, notes: str, ghidra_comment: str = ""):
        """Update function with analysis results"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE packet_functions 
            SET opcode_hex = ?, opcode_dec = ?, function_name = ?, 
                notes = ?, analysis_status = 'ANALYZED', ghidra_comment = ?
            WHERE address = ?
        ''', (opcode_hex, opcode_dec, function_name, notes, ghidra_comment, address))
        
        conn.commit()
        conn.close()
        self.update_progress()
    
    def mark_function_failed(self, address: str, error_reason: str):
        """Mark function as failed to analyze"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE packet_functions 
            SET analysis_status = 'FAILED', notes = ?
            WHERE address = ?
        ''', (f"Analysis failed: {error_reason}", address))
        
        conn.commit()
        conn.close()
    
    def update_progress(self):
        """Update analysis progress statistics"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Get counts
        cursor.execute('SELECT COUNT(*) FROM packet_functions')
        total = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM packet_functions WHERE analysis_status = "ANALYZED"')
        analyzed = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM packet_functions WHERE analysis_status = "PENDING"')
        pending = cursor.fetchone()[0]
        
        # Update progress table
        cursor.execute('DELETE FROM analysis_progress')
        cursor.execute('''
            INSERT INTO analysis_progress (total_functions, analyzed_functions, pending_functions)
            VALUES (?, ?, ?)
        ''', (total, analyzed, pending))
        
        conn.commit()
        conn.close()
    
    def get_progress(self) -> Dict:
        """Get current analysis progress"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM analysis_progress ORDER BY last_updated DESC LIMIT 1')
        row = cursor.fetchone()
        
        if row:
            progress = {
                'total_functions': row[1],
                'analyzed_functions': row[2],
                'pending_functions': row[3],
                'completion_percentage': round((row[2] / row[1]) * 100, 2) if row[1] > 0 else 0,
                'last_updated': row[4]
            }
        else:
            progress = {
                'total_functions': 0,
                'analyzed_functions': 0,
                'pending_functions': 0,
                'completion_percentage': 0,
                'last_updated': None
            }
        
        conn.close()
        return progress
    
    def export_to_csv(self, output_file: str):
        """Export all data to CSV"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT opcode_hex, opcode_dec, function_name, address, label, 
                   code_unit, context, notes, analysis_status
            FROM packet_functions 
            ORDER BY address
        ''')
        
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['Opcode_Hex', 'Opcode_Dec', 'Function_Name', 'Location', 
                           'Label', 'Code_Unit', 'Context', 'Notes', 'Analysis_Status'])
            writer.writerows(cursor.fetchall())
        
        conn.close()
        print(f"Exported data to {output_file}")

def create_analysis_task(db: PacketAnalysisDB, batch_size: int = 5) -> str:
    """Create analysis task for the AI assistant"""
    pending_functions = db.get_pending_functions(batch_size)
    progress = db.get_progress()
    
    if not pending_functions:
        return "🎉 All functions have been analyzed! No pending tasks."
    
    task = f"""
# 📋 Packet Analysis Task - Batch Analysis

## 📊 Current Progress:
- **Total Functions:** {progress['total_functions']}
- **Analyzed:** {progress['analyzed_functions']} ({progress['completion_percentage']}%)
- **Pending:** {progress['pending_functions']}

## 🎯 Task: Analyze the following {len(pending_functions)} functions

Please analyze each function using Ghidra and update the database:

"""
    
    for i, func in enumerate(pending_functions, 1):
        task += f"""
### {i}. Function at address `{func['address']}`
- **Address:** {func['address']}
- **Code Unit:** {func['code_unit']}
- **Context:** {func['context']}

**Required actions:**
1. Decompile function at address `{func['address']}`
2. Identify the opcode(s) used
3. Determine function purpose
4. Rename function appropriately
5. Add descriptive comment in Ghidra
6. Update database with results

"""
    
    task += """
## 📝 Instructions:
1. Use `decompile_function_by_address_ghidra` for each address
2. Analyze the decompiled code to identify opcodes and purpose
3. Use `rename_function_by_address_ghidra` to rename functions
4. Use `set_decompiler_comment_ghidra` to add comments
5. Update the database using the Python script

## 🔄 After Analysis:
Run the following Python code to update the database:
```python
# Update each analyzed function
db.update_function_analysis(
    address="FUNCTION_ADDRESS",
    opcode_hex="0xXX", 
    opcode_dec=XX,
    function_name="descriptive_function_name",
    notes="Brief description of function purpose",
    ghidra_comment="Detailed comment added to Ghidra"
)
```

Let's continue the systematic analysis!
"""
    
    return task

if __name__ == "__main__":
    # Initialize database
    db = PacketAnalysisDB()
    
    # Import data
    print("Importing function addresses from func file...")
    db.import_from_func_file("func")
    
    print("Importing already analyzed data...")
    db.import_analyzed_data("func_analysis_status.csv")
    
    # Show progress
    progress = db.get_progress()
    print(f"\n📊 Analysis Progress:")
    print(f"Total Functions: {progress['total_functions']}")
    print(f"Analyzed: {progress['analyzed_functions']} ({progress['completion_percentage']}%)")
    print(f"Pending: {progress['pending_functions']}")
    
    # Create analysis task
    print("\n" + "="*50)
    print("ANALYSIS TASK:")
    print("="*50)
    task = create_analysis_task(db, 5)
    print(task)
